package config

import (
	"flag"
	"io/ioutil"
	"os"
	"path/filepath"
	"runtime"

	"gitlab.com/innopm/deneb/util"
	"gopkg.in/yaml.v2"
)

// Config list all config from yaml
type Config struct {
	Database   Database       `yaml:"database"`
	Logger     Logger         `yaml:"logger"`
	App        AppConf        `yaml:"app"`
	AWS        AWSConf        `yaml:"aws"`
	DenebCmd   DenebCommand   `yaml:"deneb_cmd"`
	Notif      Notification   `yaml:"notification"`
	Integ      Integration    `yaml:"integration"`
	CalSync    CalendarSync   `yaml:"calendar_sync"`
	Mail       Mail           `yaml:"mail"`
	HubSp      HubSpot        `yaml:"hubspot"`
	Batch      Batch          `yaml:"batch"`
	Report     Report         `yaml:"report"`
	Azure      AzureConf      `yaml:"azure"`
	Timekeeper TimekeeperConf `yaml:"timekeeper"`
}

// Database list db details
type Database struct {
	Debug            bool                   `yaml:"debug"`
	Dbms             string                 `yaml:"dbms"`
	MaxIdleConns     int                    `yaml:"max_idle_conns"`
	MaxOpenConns     int                    `yaml:"max_open_conns"`
	ConnsMaxLifeTime int                    `yaml:"conns_max_lifetime"`
	Port             int                    `yaml:"port"`
	CryptKey         string                 `yaml:"crypt_key"`
	List             map[string]Credentials `yaml:"list"`
}

// Credentials maps login db details
type Credentials struct {
	User     string `yaml:"user"`
	Password string `yaml:"password"`
	Host     string `yaml:"host"`
	Name     string `yaml:"name"`
}

// Logger maps the logger config
type Logger struct {
	Level         string   `yaml:"level"`
	FilePath      string   `yaml:"filepath"`
	FileName      string   `yaml:"filename"`
	SkipUserAgent []string `yaml:"skip_user_agent"`
	ShowResponses bool     `yaml:"show_responses"`
}

// AppConf maps app config
type AppConf struct {
	Port             int      `yaml:"port"`
	Debug            bool     `yaml:"debug"`
	ServiceName      string   `yaml:"service_name"`
	WebDomains       []string `yaml:"web_domains"`
	MainDomain       string   `yaml:"main_domain"`
	DownloadDomain   string   `yaml:"download_domain"`
	AdminIPAddresses []string `yaml:"admin_ipaddresses"`
}

// AWSConf maps the aws config
type AWSConf struct {
	Credentials AWSCredentials `yaml:"credentials"`
	SQS         SQS            `yaml:"sqs"`
}

// AWSCredentials maps aws auth details
type AWSCredentials struct {
	AccessKey string `yaml:"access_key"`
	SecretKey string `yaml:"secret_key"`
	Region    string `yaml:"region"`
}

// SQS maps sqs details
type SQS struct {
	Job SQSConf `yaml:"job"`
}

// SQSConf maps queue for sqs details
type SQSConf struct {
	URL        string  `yaml:"url"`
	TimeoutSec float64 `yaml:"timeout_sec"` // to be compatible with time
}

// Notification maps notif details
type Notification struct {
	MaxRecords int `yaml:"max_records"`
}

type Integration struct {
	MaxWaitTime         int        `yaml:"max_wait_time"`
	HttpClient          HttpClient `yaml:"http_client"`
	Jobcan              Jobcan     `yaml:"jobcan"`
	KOT                 KOT        `yaml:"kot"`
	Kinkakuji           Kinkakuji  `yaml:"kinkakuji"`
	Hrmos               Hrmos      `yaml:"hrmos"`
	TargetDatesDuration int        `yaml:"target_dates_duration"`
	EncryptionKey       string     `yaml:"encryption_key"`
}

type DenebCommand struct {
	AWS AWSConf `yaml:"aws"`
}

type HttpClient struct {
	Timeout int `yaml:"time_out"`
}

type Jobcan struct {
	TokenDuration             int    `yaml:"token_duration"` // token life time (minutes)
	TokenApiUrl               string `yaml:"token_api_url"`
	SummaryApiUrl             string `yaml:"summary_api_url"`
	AditApiUrl                string `yaml:"adit_api_url"`
	EmployeeApiUrl            string `yaml:"employee_api_url"`
	RefreshApiUrl             string `yaml:"refresh_api_url"`
	RefreshCheckApiUrl        string `yaml:"refresh_check_api_url"`
	EmployeeCount             int    `yaml:"employee_count"`                     // number of employee info to get with one API call
	SummaryCount              int    `yaml:"summary_count"`                      // number of summary to get with one API call
	AditCount                 int    `yaml:"adit_count"`                         // number of adit_count
	MaxDatesDuration          int    `yaml:"max_dates_duration"`                 // number of max duration dates in Jobcan API
	RefreshExecEmployeeUnit   int    `yaml:"refresh_exec_employee_unit"`         // number of employee unit to execute refresh
	RefreshWaitDurBeforeCheck int    `yaml:"refresh_wait_duration_before_check"` // seconds
	RefreshDeadlineDuration   int    `yaml:"refresh_deadline_duration"`          // seconds
}

type KOT struct {
	ClientId                      string `yaml:"client_id"`
	ClientSecret                  string `yaml:"client_secret"`
	TokenDuration                 int    `yaml:"token_duration"` // token life time (minutes)
	AuthUrl                       string `yaml:"auth_url"`
	TokenUrl                      string `yaml:"token_url"`
	DailyWorkingsApiUrl           string `yaml:"daily_workings_api_url"`
	DailyWorkingsTimerecordApiUrl string `yaml:"daily_workings_timerecord_api_url"`
	DailySchedulesApiUrl          string `yaml:"daily_schedules_api_url"`
	EmployeeApiUrl                string `yaml:"employee_api_url"`
	EmployeeCount                 int    `yaml:"employee_count"`     // number of employee info to get with one API call
	SummaryCount                  int    `yaml:"summary_count"`      // number of summary to get with one API call
	MaxDatesDuration              int    `yaml:"max_dates_duration"` // number of max duration dates in Jobcan API
}

type AzureConf struct {
	OpenAI OpenAI `yaml:"openai"`
}

type OpenAI struct {
	APIKey    string `yaml:"api_key"`
	EndPoint  string `yaml:"endpoint"`
	Model     string `yaml:"model"`
	MaxTokens int    `yaml:"max_tokens"`
}

type Kinkakuji struct {
	ClientId                      string `yaml:"client_id"`
	ClientSecret                  string `yaml:"client_secret"`
	TokenDuration                 int    `yaml:"token_duration"` // token life time (minutes)
	AuthUrl                       string `yaml:"auth_url"`
	TokenUrl                      string `yaml:"token_url"`
	DailyWorkingsApiUrl           string `yaml:"daily_workings_api_url"`
	DailyWorkingsTimerecordApiUrl string `yaml:"daily_workings_timerecord_api_url"`
	EmployeeApiUrl                string `yaml:"employee_api_url"`
	EmployeeCount                 int    `yaml:"employee_count"`     // number of employee info to get with one API call
	SummaryCount                  int    `yaml:"summary_count"`      // number of summary to get with one API call
	MaxDatesDuration              int    `yaml:"max_dates_duration"` // number of max duration dates in Jobcan API
}

type Hrmos struct {
	TokenApiUrl                   string `yaml:"token_api_url"`
	WorkOutputsApiUrl             string `yaml:"work_outputs_api_url"`
	MaxNumberOfWorkOutputsPerCall int    `yaml:"max_number_of_work_outputs_per_call"` // number of max work_outputs to get per call
}

type CalendarSync struct {
	Google  Google  `yaml:"google"`
	Outlook Outlook `yaml:"outlook"`
}

type Google struct {
	ClientId     string   `yaml:"client_id"`
	ClientSecret string   `yaml:"client_secret"`
	Scopes       []string `yaml:"scopes"`
	AuthUrl      string   `yaml:"auth_url"`
	TokenUrl     string   `yaml:"token_url"`
}

type Outlook struct {
	ClientId     string   `yaml:"client_id"`
	ClientSecret string   `yaml:"client_secret"`
	Scopes       []string `yaml:"scopes"`
	AuthUrl      string   `yaml:"auth_url"`
	TokenUrl     string   `yaml:"token_url"`
}

type Mail struct {
	ServiceMail string `yaml:"service_mail"`
	Smtp        Smtp   `yaml:"smtp"`
}

type Smtp struct {
	Host     string `yaml:"host"`
	Port     string `yaml:"port"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
}

type HubSpot struct {
	VisitorTokenApiUrl        string     `yaml:"visitor_token_api_url"`
	VisitorTokenApiPrivateKey string     `yaml:"visitor_token_api_private_key"`
	HttpClient                HttpClient `yaml:"http_client"`
}

type Batch struct {
	WorkReportPartition WorkReportPartition `yaml:"work_report_partition"`
	WorkReportDeletion  WorkReportDeletion  `yaml:"work_report_deletion"`
}

type Report struct {
	WorkReport WorkReport `yaml:"work_report"`
}

type WorkReportPartition struct {
	Size int `yaml:"size"`
}

type WorkReport struct {
	UseRealtimeCompanies []int `yaml:"use_realtime_companies"`
}

type WorkReportDeletion struct {
	Size int `yaml:"size"`
}

type TimekeeperConf struct {
	AttDiff        AttendDiff         `yaml:"attendance_difference"`
	TimekeeperLink TimekeeperLinkConf `yaml:"timekeeper_link"`
}

type AttendDiff struct {
	CalcDays int `yaml:"calc_days"`
}

type TimekeeperLinkConf struct {
	ForPCModeUser      string `yaml:"for_pc_mode_user"`
	ForAccountNameUser string `yaml:"for_account_name_mode_user"`
	ForEmployee        string `yaml:"for_employee"`
}

// Config fields
var (
	Global     *Config
	Db         *Database
	Log        *Logger
	App        *AppConf
	AWS        *AWSConf
	DenebCmd   *DenebCommand
	Notif      *Notification
	Integ      *Integration
	CalSync    *CalendarSync
	MailConf   *Mail
	HubSp      *HubSpot
	Btch       *Batch
	Azure      *AzureConf
	Timekeeper *TimekeeperConf
	Rprt       *Report
)

// InitConfig initializes config
func InitConfig() {
	defaultFile := filepath.Join(PJROOT,
		SecretPath, ConfigFile)
	var conf string
	var testFlag bool
	if flag.Lookup("test.v") != nil {
		testFlag = true
		conf = setTestFile()
	} else {
		util.ParseFlags()
		conf = setConfigFile(defaultFile)
	}

	Load(conf, testFlag)
}

func setTestFile() (conf string) {
	testFile := filepath.Join(SecretPath, ConfigTestFile)
	testConf := setConfigFile(testFile)
	if filepath.Base(testConf) == ConfigFile {
		panic("Test config must not be same as config.yaml")
	} else {
		_, currentFilePath, _, _ := runtime.Caller(0)
		conf = filepath.Join(currentFilePath, "../../", testConf)
	}

	return
}

func setConfigFile(file string) string {
	if val, ok := util.GetFlag("config"); ok {
		return val
	}

	return file
}

// Load reads yaml file
func Load(file string, testFlag bool) {
	data, err := ioutil.ReadFile(file)
	if err != nil {
		panic("%v" + err.Error())
	}

	var cfg *Config
	err = yaml.Unmarshal(data, &cfg)
	if err != nil {
		panic("%v" + err.Error())
	}

	Global = cfg
	Db = &cfg.Database
	Log = &cfg.Logger
	App = &cfg.App
	AWS = &cfg.AWS
	DenebCmd = &cfg.DenebCmd
	Notif = &cfg.Notif
	Integ = &cfg.Integ
	CalSync = &cfg.CalSync
	MailConf = &cfg.Mail
	HubSp = &cfg.HubSp
	Btch = &cfg.Batch
	Azure = &cfg.Azure
	Timekeeper = &cfg.Timekeeper
	Rprt = &cfg.Report

	// Use Permanent storage DB for execution of UTs
	// This changes db hosts
	env := os.Getenv("FAST_UT")
	if testFlag && env != "ON" {
		testMap := map[string]Credentials{}
		for k, v := range Db.List {
			testMap[k] = Credentials{
				User:     v.User,
				Password: v.Password,
				Host:     "inpm-db",
				Name:     v.Name,
			}
		}
		Db.List = testMap
	}
}

// GetCredentials fetch specific db login credentials
func GetCredentials(name string) Credentials {
	return Db.List[name]
}

// Check if current environment is local
func IsLocalEnv() bool {
	return util.InSlice(App.WebDomains, "app.local.crowdlog.jp")
}
