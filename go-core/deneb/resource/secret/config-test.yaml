app:
  port: 8080
  debug: true
  web_domains: [app.local.crowdlog.jp]
  main_domain: app.local.crowdlog.jp
  download_domain: app.local.crowdlog.jp
  admin_ipaddresses: []

database:
  debug: true
  dbms: mysql
  max_idle_conns: 100
  max_open_conns: 16
  conns_max_lifetime: 30 # mins
  port: 3306
  crypt_key: konnyaku
  list:
    common_db:
      user: root
      password: pass
      host: inpm-db-test
      name: innopm_testcommon
    db01:
      user: root
      password: pass
      host: inpm-db-test
      name: innopm_test
    db02:
      user: root
      password: pass
      host: inpm-db-test
      name: innopm_test2

logger:
  level: debug # debug | info | warn | error | dpanic | panic | fatal
  filepath: /var/deneb/log
  filename: #
  skip_user_agent: [ELB-HealthChecker/2.0]
  show_responses: true

aws:
  credentials:
    access_key:
    secret_key:
    region: ap-northeast-1
  sqs:
    job:
      url:
      timeout_sec: 300

deneb_cmd: # localstack settings
  aws:
    credentials:
      access_key: dummy
      secret_key: dummy
      region: ap-northeast-1
    sqs:
      job:
        url: http://localstack:4566/000000000000/crowdlog-local-queue
        timeout_sec: 300

notification:
  max_records: 100

integration:
  max_wait_time: 10 # if other integration is running, how long to wait for next running
  http_client:
    time_out: 60 # seconds
  jobcan:
    token_duration: 40 # token life time (minutes)
    token_api_url: https://sandbox-api-auth-kintai.jobcan.jp/oauth/token
    summary_api_url: https://sandbox-api-kintai.jobcan.jp/attendance/v1/summaries/daily
    adit_api_url: https://sandbox-api-kintai.jobcan.jp/attendance/v1/adits
    employee_api_url: https://sandbox-api-kintai.jobcan.jp/master/v1/employees
    refresh_api_url: https://sandbox-api-kintai.jobcan.jp/attendance/v1/summaries/daily/%d/refresh
    refresh_check_api_url: https://sandbox-api-kintai.jobcan.jp/attendance/v1/summaries/daily/%d/refresh/%s
    employee_count: 100 # how many employees info to get by one API call
    summary_count: 100 # how many summaries to get by one API call
    adit_count: 100 # how many adits to get by one API call
    max_dates_duration: 31
    refresh_exec_employee_unit: 2
    # refresh_wait_duration_before_check: 1 # seconds
    # refresh_deadline_duration: 3 # seconds
  kot:
    client_id: &kot_client_id 9a2a4116b8c602f4e062bbc1
    client_secret: &kot_client_secret 87338c00923d61e930ff6adfec9cfd1e
    token_duration: &kot_token_duration 40 # token life time (minutes)
    auth_url: https://api.kingtime.jp/connect/kot/authorize
    token_url: https://api.kingtime.jp/connect/kot/token
    daily_workings_api_url: &kot_daily_workings_api_url https://api.kingtime.jp/v1.0/daily-workings
    daily_workings_timerecord_api_url: &kot_daily_workings_timerecord_api_url https://api.kingtime.jp/v1.0/daily-workings/timerecord
    daily_schedules_api_url: &kot_daily_schedules_api_url https://api.kingtime.jp/v1.0/daily-schedules
    employee_api_url: &kot_employee_api_url https://api.kingtime.jp/v1.0/employees
    max_dates_duration: &kot_max_dates_duration 40
  kinkakuji:
    client_id: *kot_client_id
    client_secret: *kot_client_secret
    token_duration: *kot_token_duration # token life time (minutes)
    auth_url: https://api.kingtime.jp/connect/nec/authorize
    token_url: https://api.kingtime.jp/connect/nec/token
    daily_workings_api_url: *kot_daily_workings_api_url
    daily_workings_timerecord_api_url: *kot_daily_workings_timerecord_api_url
    daily_schedules_api_url: *kot_daily_schedules_api_url
    employee_api_url: *kot_employee_api_url
    max_dates_duration: *kot_max_dates_duration
  hrmos:
    token_api_url: https://ieyasu.co/api/%s/v1/authentication/token
    work_outputs_api_url: https://ieyasu.co/api/%s/v1/work_outputs/daily
    max_number_of_work_outputs_per_call: 100
  target_dates_duration: 40 # in days
  encryption_key: sC1Fp7iVOLZ1YnPaMbil8fIqVhndlWt4

calendar_sync:
  google:
    client_id: ************-t8i2unhr7tpvq2lf7veort41rkqkianc.apps.googleusercontent.com
    client_secret: 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
    scopes: [https://www.googleapis.com/auth/calendar.readonly]
    auth_url: https://accounts.google.com/o/oauth2/v2/auth
    token_url: https://www.googleapis.com/oauth2/v4/token
  outlook:
    client_id: 024af247-fc77-4f5b-ad3b-b8136efedd09
    client_secret: ****************************************
    scopes: [calendars.read]
    auth_url: https://login.microsoftonline.com/common/oauth2/v2.0/authorize
    token_url: https://login.microsoftonline.com/common/oauth2/v2.0/token

mail:
  service_mail: <EMAIL>
  smtp:
    host:
    port:
    username:
    password:

hubspot:
  visitor_token_api_url: https://api.hubspot.com/conversations/v3/visitor-identification/tokens/create
  visitor_token_api_private_key:
  http_client:
    time_out: 60

batch:
  work_report_partition:
    size: 3
  work_report_deletion:
    size: 3

azure:
  openai:
    api_key:
    endpoint:
    model:
    max_tokens: 1000

timekeeper:
  attendance_difference:
    calc_days: 7 # Number of days for calculation
  timekeeper_link:
    for_pc_mode_user: https://www.timekeeper.jp/tk/hstactivelogs
    for_account_name_mode_user: https://www.timekeeper.jp/tk/accactivelogs
    for_employee: https://www.timekeeper.jp/tk/personal/selfreports

report:
  work_report:
    use_realtime_companies: [1]